# Milkdown 表格 BR 标签修复演示

## 问题描述

在 Milkdown 的表格组件中，当表格单元格内包含 `<br>` 标签时，会出现内容换行到下一个单元格的问题，导致表格样式错乱。

## 问题原因

1. **硬换行过滤插件**：`hardbreakFilterPlugin` 将 `'table'` 包含在过滤列表中，导致表格内的所有硬换行都被过滤掉
2. **表格单元格内容限制**：`cellContent: 'paragraph'` 限制了单元格只能包含单个段落
3. **BR 标签处理不当**：BR 标签无法正确转换为硬换行节点

## 修复方案

### 1. 修改硬换行过滤逻辑

```typescript
// 修改前：完全禁止表格内的硬换行
export const hardbreakFilterNodes = $ctx(
  ['table', 'code_block'],
  'hardbreakFilterNodes'
)

// 修改后：只禁止代码块内的硬换行
export const hardbreakFilterNodes = $ctx(
  ['code_block'],
  'hardbreakFilterNodes'
)
```

### 2. 增强硬换行过滤插件

```typescript
// 新增精确的表格处理逻辑
export const hardbreakFilterPlugin = $prose((ctx) => {
  const notIn = ctx.get(hardbreakFilterNodes.key)
  return new Plugin({
    key: new PluginKey('MILKDOWN_HARDBREAK_FILTER'),
    filterTransaction: (tr, state) => {
      // ... 原有逻辑
      
      // 特殊表格处理：允许在表格单元格内使用硬换行，但不允许在表格行级别使用
      if (inTableRow && !inTableCell) {
        canApply = false
      }
      
      return canApply
    },
  })
})
```

### 3. 修改表格单元格内容配置

```typescript
// 修改前：单个段落
cellContent: 'paragraph'

// 修改后：支持多个段落
cellContent: 'paragraph+'
```

### 4. 增强表格单元格解析逻辑

```typescript
// 新增智能内容处理
runner: (state, node, type) => {
  const align = node.align as string
  state.openNode(type, { alignment: align })
  
  if (node.children && node.children.length > 0) {
    const hasBlockContent = node.children.some((child: any) => 
      child.type === 'paragraph' || child.type === 'heading' || child.type === 'list'
    )
    
    if (hasBlockContent) {
      state.next(node.children)
    } else {
      state
        .openNode(state.schema.nodes.paragraph as NodeType)
        .next(node.children)
        .closeNode()
    }
  } else {
    state
      .openNode(state.schema.nodes.paragraph as NodeType)
      .closeNode()
  }
  
  state.closeNode()
}
```

## 测试用例

### 测试用例 1：基本 BR 标签处理

```markdown
| 列1 | 列2 |
|-----|-----|
| 第一行<br>第二行 | 正常内容 |
```

**期望结果**：第一个单元格内容正确换行，不会溢出到第二个单元格。

### 测试用例 2：多个 BR 标签

```markdown
| 内容 |
|------|
| 行1<br>行2<br>行3 |
```

**期望结果**：单元格内容分为三行显示。

### 测试用例 3：混合内容

```markdown
| 功能 | 描述 |
|------|------|
| **粗体**<br>*斜体* | 代码：`console.log()`<br>换行测试 |
```

**期望结果**：Markdown 格式正确渲染，BR 标签正确换行。

## 验证方法

1. **单元测试**：运行 `table-br-fix.test.ts` 中的测试用例
2. **手动测试**：在编辑器中输入包含 BR 标签的表格
3. **快捷键测试**：在表格单元格内按 `Shift+Enter` 插入硬换行
4. **序列化测试**：验证包含硬换行的表格能正确序列化为 Markdown

## 注意事项

1. **向后兼容**：修改保持了与现有 API 的兼容性
2. **性能影响**：新增的逻辑对性能影响很小
3. **边界情况**：需要测试复杂嵌套结构的表格
4. **浏览器兼容性**：确保在不同浏览器中表现一致

## 相关文件

- `packages/plugins/preset-commonmark/src/plugin/hardbreak-filter-plugin.ts`
- `packages/plugins/preset-gfm/src/node/table/schema.ts`
- `packages/plugins/preset-gfm/src/node/table/__tests__/table-br-fix.test.ts`
