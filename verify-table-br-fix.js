#!/usr/bin/env node

/**
 * 验证脚本：检查表格 BR 标签修复是否正确应用
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证 Milkdown 表格 BR 标签修复...\n');

// 检查文件是否存在并包含预期的修复内容
function checkFileContent(filePath, expectedContent, description) {
  console.log(`📁 检查文件: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return false;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  
  const checks = Array.isArray(expectedContent) ? expectedContent : [expectedContent];
  let allPassed = true;
  
  checks.forEach((check, index) => {
    if (typeof check === 'string') {
      if (content.includes(check)) {
        console.log(`  ✅ 检查 ${index + 1}: 找到预期内容`);
      } else {
        console.log(`  ❌ 检查 ${index + 1}: 未找到预期内容`);
        console.log(`     期望: ${check.substring(0, 100)}...`);
        allPassed = false;
      }
    } else if (check instanceof RegExp) {
      if (check.test(content)) {
        console.log(`  ✅ 检查 ${index + 1}: 正则匹配成功`);
      } else {
        console.log(`  ❌ 检查 ${index + 1}: 正则匹配失败`);
        console.log(`     模式: ${check}`);
        allPassed = false;
      }
    }
  });
  
  console.log(`  📋 ${description}: ${allPassed ? '✅ 通过' : '❌ 失败'}\n`);
  return allPassed;
}

// 验证检查项
const verifications = [
  {
    file: 'packages/plugins/preset-commonmark/src/plugin/hardbreak-filter-plugin.ts',
    checks: [
      "['code_block']",  // 确保 'table' 已从过滤列表中移除
      'inTableCell',     // 确保添加了表格单元格检查
      'inTableRow',      // 确保添加了表格行检查
      /Special handling for tables/  // 确保添加了注释说明
    ],
    description: '硬换行过滤插件修复'
  },
  {
    file: 'packages/plugins/preset-gfm/src/node/table/schema.ts',
    checks: [
      "cellContent: 'paragraph+'",  // 确保支持多个段落
      'hasBlockContent',            // 确保添加了智能内容处理
      /Handle cell content/          // 确保添加了注释说明
    ],
    description: '表格单元格内容支持增强'
  },
  // 注意：src/utils/parserMdToHtml.ts 文件在当前项目中不存在
  // 这可能是另一个项目的文件，暂时跳过检查
];

// 执行验证
let allVerificationsPassed = true;

verifications.forEach(verification => {
  const passed = checkFileContent(
    verification.file,
    verification.checks,
    verification.description
  );
  
  if (!passed) {
    allVerificationsPassed = false;
  }
});

// 检查测试文件是否存在
const testFiles = [
  'packages/plugins/preset-gfm/src/node/table/__tests__/table-br-fix.test.ts',
  'test-table-br-integration.js',
  'test-table-br-fix.html',
  'table-br-fix-demo.md',
  'MILKDOWN_TABLE_BR_FIX_SUMMARY.md'
];

console.log('📋 检查测试和文档文件...');
testFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} (缺失)`);
    allVerificationsPassed = false;
  }
});

// 最终结果
console.log('\n' + '='.repeat(60));
if (allVerificationsPassed) {
  console.log('🎉 所有验证通过！表格 BR 标签修复已正确应用。');
  console.log('\n📝 下一步建议:');
  console.log('1. 运行单元测试: npm test');
  console.log('2. 运行集成测试: node test-table-br-integration.js');
  console.log('3. 在浏览器中打开 test-table-br-fix.html 进行手动测试');
  console.log('4. 在实际项目中测试表格功能');
  process.exit(0);
} else {
  console.log('❌ 验证失败！请检查修复是否正确应用。');
  process.exit(1);
}
