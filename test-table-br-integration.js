/**
 * 集成测试：验证表格中 BR 标签的处理
 * 这个测试文件可以在 Node.js 环境中运行，验证修复效果
 */

// 模拟 milkdown 的表格解析逻辑
function parseTableWithBR(markdown) {
  console.log('原始 Markdown:');
  console.log(markdown);
  console.log('\n' + '='.repeat(50) + '\n');

  // 模拟预处理逻辑（来自 src/utils/parserMdToHtml.ts）
  let processedMdContent = markdown;
  
  // 检查是否包含表格语法
  const hasTable = /\|.*\|/.test(markdown);
  
  if (!hasTable) {
    // 非表格内容，直接替换 br 标签为换行符
    processedMdContent = markdown.replace(/<br\s*\/?>/gi, '\n');
    console.log('非表格内容，BR 标签已替换为换行符');
  } else {
    // 表格内容，需要更精确的处理
    const lines = markdown.split('\n');
    const processedLines = lines.map(line => {
      // 如果不是表格行，替换 br 标签
      if (!line.includes('|') || /^\s*\|[\s\-\|:]+\|\s*$/.test(line)) {
        return line.replace(/<br\s*\/?>/gi, '\n');
      }
      // 表格行，保留 br 标签
      return line;
    });
    processedMdContent = processedLines.join('\n');
    console.log('表格内容，保留表格行中的 BR 标签');
  }

  console.log('预处理后的 Markdown:');
  console.log(processedMdContent);
  console.log('\n' + '='.repeat(50) + '\n');

  // 模拟表格解析
  const lines = processedMdContent.split('\n').filter(line => line.trim());
  const tableRows = [];
  
  lines.forEach((line, index) => {
    if (line.includes('|')) {
      const cells = line.split('|').slice(1, -1).map(cell => cell.trim());
      
      // 跳过分隔行
      if (index === 1 && line.includes('---')) {
        return;
      }
      
      const processedCells = cells.map(cell => {
        // 检查是否包含 BR 标签
        if (cell.includes('<br')) {
          console.log(`发现包含 BR 标签的单元格: "${cell}"`);
          
          // 模拟硬换行处理
          const parts = cell.split(/<br\s*\/?>/gi);
          console.log(`  拆分为 ${parts.length} 部分:`, parts);
          
          return {
            original: cell,
            parts: parts,
            hasHardbreak: true
          };
        }
        
        return {
          original: cell,
          parts: [cell],
          hasHardbreak: false
        };
      });
      
      tableRows.push({
        isHeader: index === 0,
        cells: processedCells
      });
    }
  });

  return {
    originalMarkdown: markdown,
    processedMarkdown: processedMdContent,
    tableRows: tableRows,
    hasTable: hasTable
  };
}

// 测试用例
const testCases = [
  {
    name: '基本 BR 标签测试',
    markdown: `| 列1 | 列2 |
|-----|-----|
| 第一行<br>第二行 | 正常内容 |`
  },
  {
    name: '多个 BR 标签测试',
    markdown: `| 内容 |
|------|
| 行1<br>行2<br>行3 |`
  },
  {
    name: '混合内容测试',
    markdown: `| 功能 | 描述 |
|------|------|
| **粗体**<br>*斜体* | 代码：\`console.log()\`<br>换行测试 |`
  },
  {
    name: '不同 BR 格式测试',
    markdown: `| 类型 | 内容 |
|------|------|
| BR标签 | 内容1<br/>内容2 |
| BR标签 | 内容A<br>内容B |
| 混合 | 开始<br />中间<br>结束 |`
  },
  {
    name: '非表格内容测试',
    markdown: `这是普通段落<br>包含换行

另一个段落<br/>也有换行`
  }
];

// 运行测试
console.log('🧪 开始测试表格 BR 标签处理\n');

testCases.forEach((testCase, index) => {
  console.log(`📋 测试用例 ${index + 1}: ${testCase.name}`);
  console.log('-'.repeat(60));
  
  try {
    const result = parseTableWithBR(testCase.markdown);
    
    if (result.hasTable) {
      console.log('✅ 检测到表格内容');
      console.log(`📊 解析出 ${result.tableRows.length} 行表格数据`);
      
      result.tableRows.forEach((row, rowIndex) => {
        console.log(`  行 ${rowIndex + 1} (${row.isHeader ? '表头' : '数据'}):`)
        row.cells.forEach((cell, cellIndex) => {
          if (cell.hasHardbreak) {
            console.log(`    单元格 ${cellIndex + 1}: ✅ 包含硬换行`);
            console.log(`      原始: "${cell.original}"`);
            console.log(`      拆分: [${cell.parts.map(p => `"${p}"`).join(', ')}]`);
          } else {
            console.log(`    单元格 ${cellIndex + 1}: "${cell.original}"`);
          }
        });
      });
      
      // 检查是否有内容溢出风险
      const hasOverflowRisk = result.tableRows.some(row => 
        row.cells.some(cell => 
          cell.original.includes('|') && !cell.original.includes('`')
        )
      );
      
      if (hasOverflowRisk) {
        console.log('⚠️  警告：检测到可能的内容溢出风险');
      } else {
        console.log('✅ 无内容溢出风险');
      }
    } else {
      console.log('ℹ️  非表格内容，BR 标签已转换为换行符');
    }
    
  } catch (error) {
    console.log('❌ 测试失败:', error.message);
  }
  
  console.log('\n' + '='.repeat(80) + '\n');
});

console.log('🎉 测试完成！');

// 导出测试函数以供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { parseTableWithBR, testCases };
}
