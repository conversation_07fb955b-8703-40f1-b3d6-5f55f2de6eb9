import { describe, expect, it } from 'vitest'
import { Editor } from '@milkdown/core'
import { commonmark } from '@milkdown/preset-commonmark'
import { gfm } from '@milkdown/preset-gfm'
import { nord } from '@milkdown/theme-nord'

describe('Table BR Tag Fix', () => {
  let editor: Editor

  beforeEach(async () => {
    editor = await Editor.make()
      .config(nord)
      .use(commonmark)
      .use(gfm)
      .create()
  })

  afterEach(() => {
    editor?.destroy()
  })

  it('should handle BR tags in table cells without content overflow', async () => {
    const markdown = `| Column 1 | Column 2 |
|----------|----------|
| Line 1<br>Line 2 | Normal content |
| Regular | Multi<br>line<br>content |`

    const result = await editor.action((ctx) => {
      const parser = ctx.get('parserCtx')
      return parser(markdown)
    })

    // 检查是否正确解析了表格结构
    expect(result.type.name).toBe('doc')
    
    const table = result.firstChild
    expect(table?.type.name).toBe('table')
    
    // 检查表格行数
    expect(table?.childCount).toBe(2) // header row + data row
    
    // 检查第一个数据行的第一个单元格
    const firstDataRow = table?.child(1)
    expect(firstDataRow?.type.name).toBe('table_row')
    
    const firstCell = firstDataRow?.child(0)
    expect(firstCell?.type.name).toBe('table_cell')
    
    // 检查单元格内容是否包含硬换行
    const cellContent = firstCell?.child(0)
    expect(cellContent?.type.name).toBe('paragraph')
    
    // 验证硬换行节点的存在
    let hasHardbreak = false
    cellContent?.forEach((node) => {
      if (node.type.name === 'hardbreak') {
        hasHardbreak = true
      }
    })
    expect(hasHardbreak).toBe(true)
  })

  it('should allow hardbreak insertion in table cells via Shift+Enter', async () => {
    const markdown = `| Test |
|------|
| Content |`

    await editor.action((ctx) => {
      const parser = ctx.get('parserCtx')
      const doc = parser(markdown)
      
      // 模拟在表格单元格中按 Shift+Enter
      const tr = ctx.get('editorStateCtx').tr
      const pos = 15 // 假设这是表格单元格内的位置
      
      // 尝试插入硬换行
      tr.setMeta('hardbreak', true)
      tr.replaceSelectionWith(ctx.get('schemaCtx').nodes.hardbreak.create())
      
      // 验证事务是否被允许
      const filterResult = ctx.get('editorViewCtx').state.plugins
        .find(p => p.key === 'MILKDOWN_HARDBREAK_FILTER')
        ?.props.filterTransaction?.(tr, ctx.get('editorStateCtx'))
      
      expect(filterResult).toBe(true)
    })
  })

  it('should prevent hardbreak insertion directly in table rows', async () => {
    const markdown = `| Test |
|------|
| Content |`

    await editor.action((ctx) => {
      const parser = ctx.get('parserCtx')
      const doc = parser(markdown)
      
      // 模拟在表格行级别（而非单元格内）尝试插入硬换行
      const tr = ctx.get('editorStateCtx').tr
      const pos = 10 // 假设这是表格行级别的位置
      
      // 尝试插入硬换行
      tr.setMeta('hardbreak', true)
      tr.replaceSelectionWith(ctx.get('schemaCtx').nodes.hardbreak.create())
      
      // 验证事务是否被阻止
      const filterResult = ctx.get('editorViewCtx').state.plugins
        .find(p => p.key === 'MILKDOWN_HARDBREAK_FILTER')
        ?.props.filterTransaction?.(tr, ctx.get('editorStateCtx'))
      
      expect(filterResult).toBe(false)
    })
  })

  it('should serialize table with hardbreaks back to markdown correctly', async () => {
    const originalMarkdown = `| Column 1 | Column 2 |
|----------|----------|
| Line 1<br>Line 2 | Normal |`

    const result = await editor.action(async (ctx) => {
      const parser = ctx.get('parserCtx')
      const serializer = ctx.get('serializerCtx')
      
      const doc = parser(originalMarkdown)
      const serializedMarkdown = serializer(doc)
      
      return serializedMarkdown
    })

    // 验证序列化后的 markdown 保持了表格结构
    expect(result).toContain('| Column 1 | Column 2 |')
    expect(result).toContain('|----------|----------|')
    
    // 验证硬换行被正确序列化（可能转换为实际的换行符或保持为 <br>）
    expect(result).toMatch(/Line 1[\s\S]*Line 2/)
  })

  it('should handle multiple paragraphs in table cells', async () => {
    const markdown = `| Multi-paragraph |
|-----------------|
| Para 1<br><br>Para 2 |`

    const result = await editor.action((ctx) => {
      const parser = ctx.get('parserCtx')
      return parser(markdown)
    })

    const table = result.firstChild
    const firstDataRow = table?.child(1)
    const firstCell = firstDataRow?.child(0)
    
    // 验证单元格可以包含多个段落或硬换行
    expect(firstCell?.childCount).toBeGreaterThan(0)
    
    // 检查是否正确处理了连续的硬换行
    let hardbreakCount = 0
    firstCell?.forEach((node) => {
      node.forEach((child) => {
        if (child.type.name === 'hardbreak') {
          hardbreakCount++
        }
      })
    })
    
    expect(hardbreakCount).toBeGreaterThan(0)
  })
})
