import { Plugin, Plugin<PERSON><PERSON> } from '@milkdown/prose/state'
import { $ctx, $prose } from '@milkdown/utils'
import { withMeta } from '../__internal__'

/// This slice contains the nodes that within which the hardbreak will be ignored.
export const hardbreakFilterNodes = $ctx(
  ['code_block'],
  'hardbreakFilterNodes'
)

withMeta(hardbreakFilterNodes, {
  displayName: 'Ctx<hardbreakFilterNodes>',
  group: 'Prose',
})

/// This plugin is used to filter the hardbreak node.
/// If the hardbreak is going to be inserted within a node that is in the `hardbreakFilterNodes`, ignore it.
/// Special handling for tables: allow hardbreaks in table cells but not in table rows directly.
export const hardbreakFilterPlugin = $prose((ctx) => {
  const notIn = ctx.get(hardbreakFilterNodes.key)
  return new Plugin({
    key: new PluginKey('MILKDOWN_HARDBREAK_FILTER'),
    filterTransaction: (tr, state) => {
      const isInsertHr = tr.getMeta('hardbreak')
      const [step] = tr.steps
      if (isInsertHr && step) {
        const { from } = step as unknown as { from: number }
        const $from = state.doc.resolve(from)
        let curDepth = $from.depth
        let canApply = true
        let inTableCell = false
        let inTableRow = false

        // Check the node hierarchy
        while (curDepth > 0) {
          const nodeName = $from.node(curDepth).type.name

          // Track if we're in a table cell or table row
          if (nodeName === 'table_cell' || nodeName === 'table_header') {
            inTableCell = true
          }
          if (nodeName === 'table_row' || nodeName === 'table_header_row') {
            inTableRow = true
          }

          // Apply original filter logic for non-table nodes
          if (notIn.includes(nodeName)) {
            canApply = false
          }

          curDepth--
        }

        // Special table handling: allow hardbreaks in table cells but not directly in table rows
        if (inTableRow && !inTableCell) {
          canApply = false
        }

        return canApply
      }
      return true
    },
  })
})

withMeta(hardbreakFilterPlugin, {
  displayName: 'Prose<hardbreakFilterPlugin>',
  group: 'Prose',
})
