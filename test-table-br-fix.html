<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Milkdown Table BR Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .editor {
            border: 1px solid #ccc;
            min-height: 400px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .markdown-input {
            width: 100%;
            height: 150px;
            font-family: monospace;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 10px 5px 10px 0;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 3px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>Milkdown 表格 BR 标签修复测试</h1>
    
    <div class="test-section">
        <h3>测试用例 1: 表格单元格内包含 BR 标签</h3>
        <textarea class="markdown-input" id="test1">
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 第一行<br>换行内容 | 正常内容 | 另一个<br>换行测试 |
| 普通内容 | 多行<br>内容<br>测试 | 结束 |
        </textarea>
        <button onclick="testMarkdown('test1', 'result1')">测试渲染</button>
        <div class="result" id="result1"></div>
    </div>

    <div class="test-section">
        <h3>测试用例 2: 表格单元格内包含不同类型的换行</h3>
        <textarea class="markdown-input" id="test2">
| 类型 | 内容 | 说明 |
|------|------|------|
| BR标签 | 内容1<br/>内容2 | 使用自闭合BR |
| BR标签 | 内容A<br>内容B | 使用普通BR |
| 混合 | 开始<br/>中间<br>结束 | 混合使用 |
        </textarea>
        <button onclick="testMarkdown('test2', 'result2')">测试渲染</button>
        <div class="result" id="result2"></div>
    </div>

    <div class="test-section">
        <h3>测试用例 3: 复杂表格内容</h3>
        <textarea class="markdown-input" id="test3">
| 功能 | 描述 | 示例 |
|------|------|------|
| 硬换行 | 支持单元格内换行<br>保持表格结构完整 | 第一行<br>第二行<br>第三行 |
| **粗体** | 支持markdown格式<br>*斜体*也可以 | **重要**<br>*提示* |
| `代码` | 行内代码<br>`console.log()` | `var x = 1`<br>`return x` |
        </textarea>
        <button onclick="testMarkdown('test3', 'result3')">测试渲染</button>
        <div class="result" id="result3"></div>
    </div>

    <div class="editor" id="editor"></div>

    <script type="module">
        // 这里需要导入实际的 milkdown 库
        // 由于这是测试文件，我们先用简单的模拟
        
        window.testMarkdown = function(inputId, resultId) {
            const input = document.getElementById(inputId);
            const result = document.getElementById(resultId);
            const markdown = input.value.trim();
            
            // 简单的 markdown 表格解析模拟
            // 实际应该使用 milkdown 的解析器
            const lines = markdown.split('\n').filter(line => line.trim());
            let html = '<table>';
            
            lines.forEach((line, index) => {
                if (line.includes('|')) {
                    const cells = line.split('|').slice(1, -1).map(cell => cell.trim());
                    const tag = index === 0 ? 'th' : 'td';
                    
                    if (index === 1 && line.includes('---')) {
                        return; // 跳过分隔行
                    }
                    
                    html += '<tr>';
                    cells.forEach(cell => {
                        // 处理 BR 标签
                        const processedCell = cell
                            .replace(/<br\s*\/?>/gi, '<br>')
                            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                            .replace(/\*(.*?)\*/g, '<em>$1</em>')
                            .replace(/`(.*?)`/g, '<code>$1</code>');
                        html += `<${tag}>${processedCell}</${tag}>`;
                    });
                    html += '</tr>';
                }
            });
            
            html += '</table>';
            result.innerHTML = html;
            
            // 检查是否有内容溢出到下一个单元格
            const table = result.querySelector('table');
            if (table) {
                const cells = table.querySelectorAll('td, th');
                let hasOverflow = false;
                
                cells.forEach(cell => {
                    const content = cell.innerHTML;
                    if (content.includes('|') && !content.includes('<code>')) {
                        hasOverflow = true;
                        cell.style.backgroundColor = '#ffebee';
                        cell.title = '检测到可能的内容溢出';
                    }
                });
                
                if (hasOverflow) {
                    result.innerHTML += '<p style="color: red;">⚠️ 检测到可能的表格内容溢出问题</p>';
                } else {
                    result.innerHTML += '<p style="color: green;">✅ 表格渲染正常，无内容溢出</p>';
                }
            }
        };
        
        // 初始化编辑器的占位符
        document.getElementById('editor').innerHTML = `
            <p>这里应该是 Milkdown 编辑器实例</p>
            <p>请在实际环境中集成 Milkdown 来测试修复效果</p>
        `;
    </script>
</body>
</html>
